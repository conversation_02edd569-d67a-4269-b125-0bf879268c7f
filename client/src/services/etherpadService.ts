/**
 * Etherpad API Service for Emma Studio
 * Manages pads, users, and groups for project-specific content creation
 */

interface EtherpadConfig {
  apiUrl: string;
  apiKey: string;
}

interface PadInfo {
  padID: string;
  groupID?: string;
  text?: string;
  html?: string;
  lastEdited?: number;
  usersCount?: number;
}

interface GroupInfo {
  groupID: string;
  name: string;
  pads: string[];
}

interface AuthorInfo {
  authorID: string;
  name: string;
  colorId?: string;
}

class EtherpadService {
  private config: EtherpadConfig;
  private baseUrl: string;

  constructor(config?: Partial<EtherpadConfig>) {
    this.config = {
      apiUrl: config?.apiUrl || '/api/etherpad',
      apiKey: config?.apiKey || 'emma-studio-key'
    };
    this.baseUrl = this.config.apiUrl;
  }

  /**
   * Make API request to Etherpad
   */
  private async makeRequest(endpoint: string, params: Record<string, any> = {}): Promise<any> {
    const url = new URL(`${this.baseUrl}/${endpoint}`);
    
    // Add API key and parameters
    url.searchParams.append('apikey', this.config.apiKey);
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        url.searchParams.append(key, String(value));
      }
    });

    try {
      const response = await fetch(url.toString(), {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Etherpad API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      
      if (data.code !== 0) {
        throw new Error(`Etherpad API error: ${data.message || 'Unknown error'}`);
      }

      return data.data;
    } catch (error) {
      console.error('Etherpad API request failed:', error);
      throw error;
    }
  }

  /**
   * Create a new group for a project
   */
  async createGroup(projectId: string): Promise<GroupInfo> {
    const data = await this.makeRequest('createGroup');
    return {
      groupID: data.groupID,
      name: `project-${projectId}`,
      pads: []
    };
  }

  /**
   * Create a new pad for content creation
   */
  async createPad(padName: string, projectId?: string, initialText?: string): Promise<PadInfo> {
    const padID = projectId ? `${projectId}-${padName}` : padName;
    
    const params: Record<string, any> = { padID };
    if (initialText) {
      params.text = initialText;
    }

    await this.makeRequest('createPad', params);
    
    return {
      padID,
      text: initialText || '',
      lastEdited: Date.now(),
      usersCount: 0
    };
  }

  /**
   * Get pad content as HTML
   */
  async getPadHTML(padID: string): Promise<string> {
    const data = await this.makeRequest('getHTML', { padID });
    return data.html || '';
  }

  /**
   * Get pad content as plain text
   */
  async getPadText(padID: string): Promise<string> {
    const data = await this.makeRequest('getText', { padID });
    return data.text || '';
  }

  /**
   * Set pad content from HTML
   */
  async setPadHTML(padID: string, html: string): Promise<void> {
    await this.makeRequest('setHTML', { padID, html });
  }

  /**
   * Set pad content from plain text
   */
  async setPadText(padID: string, text: string): Promise<void> {
    await this.makeRequest('setText', { padID, text });
  }

  /**
   * Delete a pad
   */
  async deletePad(padID: string): Promise<void> {
    await this.makeRequest('deletePad', { padID });
  }

  /**
   * Get pad info including last edited time and user count
   */
  async getPadInfo(padID: string): Promise<PadInfo> {
    try {
      const [html, lastEdited, usersCount] = await Promise.all([
        this.getPadHTML(padID),
        this.makeRequest('getLastEdited', { padID }),
        this.makeRequest('padUsersCount', { padID })
      ]);

      return {
        padID,
        html,
        lastEdited: lastEdited.lastEdited,
        usersCount: usersCount.padUsersCount
      };
    } catch (error) {
      console.error('Failed to get pad info:', error);
      return {
        padID,
        html: '',
        lastEdited: 0,
        usersCount: 0
      };
    }
  }

  /**
   * Create an author for user identification
   */
  async createAuthor(name: string): Promise<AuthorInfo> {
    const data = await this.makeRequest('createAuthor', { name });
    return {
      authorID: data.authorID,
      name
    };
  }

  /**
   * Get list of all pads for a project
   */
  async getProjectPads(projectId: string): Promise<PadInfo[]> {
    try {
      const data = await this.makeRequest('listAllPads');
      const projectPads = data.padIDs.filter((padID: string) => 
        padID.startsWith(`${projectId}-`)
      );

      const padInfos = await Promise.all(
        projectPads.map((padID: string) => this.getPadInfo(padID))
      );

      return padInfos;
    } catch (error) {
      console.error('Failed to get project pads:', error);
      return [];
    }
  }

  /**
   * Generate pad URL for embedding
   */
  getPadUrl(padID: string, options: {
    showControls?: boolean;
    showChat?: boolean;
    showLineNumbers?: boolean;
    useMonospaceFont?: boolean;
    userName?: string;
    userColor?: string;
  } = {}): string {
    const params = new URLSearchParams();
    
    if (options.showControls === false) params.append('showControls', 'false');
    if (options.showChat === false) params.append('showChat', 'false');
    if (options.showLineNumbers === false) params.append('showLineNumbers', 'false');
    if (options.useMonospaceFont) params.append('useMonospaceFont', 'true');
    if (options.userName) params.append('userName', options.userName);
    if (options.userColor) params.append('userColor', options.userColor);

    const queryString = params.toString();
    return `${this.baseUrl}/p/${padID}${queryString ? `?${queryString}` : ''}`;
  }

  /**
   * Check if Etherpad service is available
   */
  async checkHealth(): Promise<boolean> {
    try {
      await this.makeRequest('checkToken');
      return true;
    } catch (error) {
      console.error('Etherpad health check failed:', error);
      return false;
    }
  }

  /**
   * Get pad statistics
   */
  async getPadStats(padID: string): Promise<{
    revisions: number;
    users: number;
    chatMessages: number;
  }> {
    try {
      const [revisions, users, chatMessages] = await Promise.all([
        this.makeRequest('getRevisionsCount', { padID }),
        this.makeRequest('padUsersCount', { padID }),
        this.makeRequest('getChatHistory', { padID })
      ]);

      return {
        revisions: revisions.revisions || 0,
        users: users.padUsersCount || 0,
        chatMessages: chatMessages.messages?.length || 0
      };
    } catch (error) {
      console.error('Failed to get pad stats:', error);
      return {
        revisions: 0,
        users: 0,
        chatMessages: 0
      };
    }
  }
}

// Create singleton instance
export const etherpadService = new EtherpadService();

// Export types
export type { PadInfo, GroupInfo, AuthorInfo, EtherpadConfig };

export default EtherpadService;
