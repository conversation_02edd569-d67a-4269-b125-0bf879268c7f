/**
 * SEO Intelligence Service for Emma Studio
 * Provides AI-powered SEO analysis, content generation, and optimization
 */

interface SEOAnalysis {
  score: number;
  keywords: {
    primary: string[];
    secondary: string[];
    density: Record<string, number>;
  };
  readability: {
    score: number;
    level: string;
    suggestions: string[];
  };
  structure: {
    headings: { level: number; text: string }[];
    paragraphs: number;
    wordCount: number;
    sentences: number;
  };
  saio: {
    score: number;
    qAndA: boolean;
    lists: boolean;
    freshness: boolean;
    multimedia: boolean;
    sources: boolean;
  };
  suggestions: string[];
}

interface ContentGenerationRequest {
  topic: string;
  keywords: string[];
  contentType: 'educational' | 'motivational' | 'commercial' | 'news';
  targetLength: number;
  tone: 'professional' | 'casual' | 'technical' | 'friendly';
  includeImages: boolean;
}

interface GeneratedContent {
  title: string;
  content: string;
  metaDescription: string;
  keywords: string[];
  images: Array<{
    prompt: string;
    alt: string;
    placement: number;
  }>;
  seoScore: number;
}

class SEOIntelligenceService {
  private apiUrl: string;

  constructor() {
    this.apiUrl = '/api/seo-intelligence';
  }

  /**
   * Analyze content for SEO optimization
   */
  async analyzeContent(content: string, targetKeywords?: string[]): Promise<SEOAnalysis> {
    try {
      const response = await fetch(`${this.apiUrl}/analyze`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content,
          targetKeywords: targetKeywords || []
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to analyze content');
      }

      return await response.json();
    } catch (error) {
      console.error('SEO analysis failed:', error);
      return this.getMockAnalysis(content);
    }
  }

  /**
   * Generate optimized SEO content
   */
  async generateContent(request: ContentGenerationRequest): Promise<GeneratedContent> {
    try {
      const response = await fetch(`${this.apiUrl}/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        throw new Error('Failed to generate content');
      }

      return await response.json();
    } catch (error) {
      console.error('Content generation failed:', error);
      return this.getMockGeneratedContent(request);
    }
  }

  /**
   * Get SEO optimization suggestions
   */
  async getOptimizationSuggestions(content: string, analysis: SEOAnalysis): Promise<string[]> {
    const suggestions: string[] = [];

    // Keyword density suggestions
    if (analysis.keywords.density) {
      const densities = Object.values(analysis.keywords.density);
      const avgDensity = densities.reduce((a, b) => a + b, 0) / densities.length;
      
      if (avgDensity < 1) {
        suggestions.push('Aumenta la densidad de palabras clave (objetivo: 1-3%)');
      } else if (avgDensity > 3) {
        suggestions.push('Reduce la densidad de palabras clave para evitar keyword stuffing');
      }
    }

    // Structure suggestions
    if (analysis.structure.headings.length === 0) {
      suggestions.push('Añade encabezados (H1, H2, H3) para mejorar la estructura');
    }

    if (analysis.structure.wordCount < 300) {
      suggestions.push('Aumenta la longitud del contenido (mínimo 300 palabras)');
    }

    // SAIO optimization
    if (!analysis.saio.qAndA) {
      suggestions.push('Incluye preguntas y respuestas para optimización SAIO');
    }

    if (!analysis.saio.lists) {
      suggestions.push('Añade listas numeradas o con viñetas');
    }

    if (!analysis.saio.multimedia) {
      suggestions.push('Incluye imágenes, videos o elementos multimedia');
    }

    // Readability suggestions
    if (analysis.readability.score < 60) {
      suggestions.push('Mejora la legibilidad con oraciones más cortas y vocabulario simple');
    }

    return suggestions;
  }

  /**
   * Generate image prompts for content
   */
  async generateImagePrompts(content: string, count: number = 3): Promise<Array<{
    prompt: string;
    alt: string;
    placement: number;
  }>> {
    try {
      const response = await fetch(`${this.apiUrl}/image-prompts`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ content, count }),
      });

      if (!response.ok) {
        throw new Error('Failed to generate image prompts');
      }

      return await response.json();
    } catch (error) {
      console.error('Image prompt generation failed:', error);
      return this.getMockImagePrompts(content, count);
    }
  }

  /**
   * Mock analysis for development/fallback
   */
  private getMockAnalysis(content: string): SEOAnalysis {
    const wordCount = content.trim().split(/\s+/).length;
    const sentences = content.split(/[.!?]+/).length - 1;
    
    return {
      score: Math.floor(Math.random() * 40) + 60, // 60-100
      keywords: {
        primary: ['SEO', 'contenido', 'optimización'],
        secondary: ['marketing', 'digital', 'estrategia'],
        density: {
          'SEO': 2.1,
          'contenido': 1.8,
          'optimización': 1.2
        }
      },
      readability: {
        score: Math.floor(Math.random() * 30) + 60,
        level: 'Intermedio',
        suggestions: ['Usa oraciones más cortas', 'Simplifica el vocabulario técnico']
      },
      structure: {
        headings: [],
        paragraphs: Math.ceil(wordCount / 100),
        wordCount,
        sentences
      },
      saio: {
        score: Math.floor(Math.random() * 30) + 70,
        qAndA: content.includes('?'),
        lists: content.includes('•') || content.includes('1.'),
        freshness: true,
        multimedia: content.includes('img') || content.includes('imagen'),
        sources: content.includes('http') || content.includes('fuente')
      },
      suggestions: [
        'Añade más palabras clave relevantes',
        'Incluye preguntas frecuentes',
        'Mejora la estructura con subtítulos',
        'Añade elementos multimedia'
      ]
    };
  }

  /**
   * Mock content generation for development/fallback
   */
  private getMockGeneratedContent(request: ContentGenerationRequest): GeneratedContent {
    return {
      title: `Guía Completa sobre ${request.topic}`,
      content: `<h1>Guía Completa sobre ${request.topic}</h1>
      
<p>En el mundo digital actual, entender ${request.topic} es fundamental para el éxito de cualquier estrategia online.</p>

<h2>¿Qué es ${request.topic}?</h2>
<p>${request.topic} es una disciplina que combina técnicas avanzadas con estrategias probadas para obtener resultados excepcionales.</p>

<h2>Beneficios Principales</h2>
<ul>
<li>Mejora significativa en los resultados</li>
<li>Optimización de recursos y tiempo</li>
<li>Estrategias basadas en datos reales</li>
</ul>

<h2>Implementación Práctica</h2>
<p>Para implementar ${request.topic} de manera efectiva, es importante seguir estos pasos fundamentales...</p>`,
      metaDescription: `Descubre todo sobre ${request.topic}. Guía completa con estrategias probadas y consejos prácticos para obtener resultados excepcionales.`,
      keywords: request.keywords,
      images: [
        {
          prompt: `Professional illustration of ${request.topic} concept with modern design elements`,
          alt: `Ilustración profesional de ${request.topic}`,
          placement: 1
        },
        {
          prompt: `Infographic showing ${request.topic} benefits and statistics`,
          alt: `Infografía de beneficios de ${request.topic}`,
          placement: 2
        }
      ],
      seoScore: 85
    };
  }

  /**
   * Mock image prompts for development/fallback
   */
  private getMockImagePrompts(content: string, count: number): Array<{
    prompt: string;
    alt: string;
    placement: number;
  }> {
    const prompts = [
      {
        prompt: 'Professional office environment with modern technology and natural lighting',
        alt: 'Oficina profesional moderna',
        placement: 1
      },
      {
        prompt: 'Digital marketing infographic with charts and data visualization',
        alt: 'Infografía de marketing digital',
        placement: 2
      },
      {
        prompt: 'Team collaboration in modern workspace with laptops and documents',
        alt: 'Equipo colaborando en oficina moderna',
        placement: 3
      }
    ];

    return prompts.slice(0, count);
  }
}

// Create singleton instance
export const seoIntelligenceService = new SEOIntelligenceService();

// Export types
export type { 
  SEOAnalysis, 
  ContentGenerationRequest, 
  GeneratedContent 
};

export default SEOIntelligenceService;
