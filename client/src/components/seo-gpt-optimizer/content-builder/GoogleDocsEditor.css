/* Google Docs-Style Editor for Emma Studio */

.google-docs-editor {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f8f9fa;
  font-family: '<PERSON>o', 'Arial', sans-serif;
}

/* Header Styles */
.docs-header {
  background: white;
  border-bottom: 1px solid #e8eaed;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.docs-menu-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  min-height: 48px;
}

.docs-action-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  background: transparent;
  color: #5f6368;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.docs-action-btn:hover {
  background: #f1f3f4;
  color: #202124;
}

.docs-nav-menu {
  padding: 0 16px;
  border-top: 1px solid #e8eaed;
}

.nav-menu-item {
  padding: 8px 12px;
  border: none;
  background: transparent;
  color: #5f6368;
  font-size: 14px;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.nav-menu-item:hover {
  background: #f1f3f4;
  color: #202124;
}

/* Toolbar Styles */
.docs-toolbar {
  background: white;
  border-bottom: 1px solid #e8eaed;
  padding: 8px 16px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.toolbar-row {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.toolbar-group {
  display: flex;
  align-items: center;
  gap: 2px;
}

.toolbar-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 4px;
  background: transparent;
  color: #5f6368;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.toolbar-btn:hover {
  background: #f1f3f4;
  color: #202124;
}

.toolbar-btn:active {
  background: #e8eaed;
}

.toolbar-divider {
  width: 1px;
  height: 24px;
  background: #e8eaed;
  margin: 0 4px;
}

/* Emma AI Button */
.emma-ai-btn {
  background: linear-gradient(135deg, #3018ef 0%, #dd3a5a 100%);
  color: white !important;
  border-radius: 6px;
  width: auto;
  padding: 0 8px;
  gap: 4px;
}

.emma-ai-btn:hover {
  background: linear-gradient(135deg, #2614d4 0%, #c8334f 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(48, 24, 239, 0.3);
}

/* Select Styles */
.font-select, .size-select, .zoom-select {
  border: 1px solid #dadce0;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 14px;
  background: white;
  color: #202124;
  cursor: pointer;
  min-width: 80px;
}

.font-select {
  min-width: 120px;
}

.size-select {
  min-width: 60px;
}

.zoom-select {
  min-width: 70px;
}

/* Document Area */
.docs-document-area {
  flex: 1;
  overflow-y: auto;
  background: #f8f9fa;
  padding: 20px;
  display: flex;
  justify-content: center;
}

.docs-page-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  transform-origin: top center;
  transition: zoom 0.2s ease;
}

.docs-page {
  width: 8.5in;  /* Standard letter width */
  min-height: 11in; /* Standard letter height */
  background: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border-radius: 2px;
  position: relative;
  margin: 0 auto;
}

.docs-page-content {
  width: 100%;
  height: 100%;
  outline: none;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.docs-page-content:focus {
  outline: none;
}

/* Status Bar */
.docs-status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: white;
  border-top: 1px solid #e8eaed;
  font-size: 13px;
  color: #5f6368;
  min-height: 40px;
}

.status-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 4px;
  background: transparent;
  color: #5f6368;
  cursor: pointer;
  transition: all 0.2s ease;
}

.status-action-btn:hover {
  background: #f1f3f4;
  color: #202124;
}

/* Color Picker Group */
.color-picker-group {
  display: flex;
  align-items: center;
  gap: 1px;
}

.color-picker-group .toolbar-btn {
  border-radius: 0;
  gap: 2px;
}

.color-picker-group .toolbar-btn:first-child {
  border-radius: 4px 0 0 4px;
}

.color-picker-group .toolbar-btn:last-child {
  border-radius: 0 4px 4px 0;
}

/* Emma Branding */
.emma-text-gradient {
  background: linear-gradient(135deg, #3018ef 0%, #dd3a5a 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .docs-page {
    width: 95%;
    max-width: 8.5in;
  }
  
  .docs-document-area {
    padding: 10px;
  }
}

@media (max-width: 768px) {
  .docs-menu-bar {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }
  
  .toolbar-row {
    flex-wrap: wrap;
  }
  
  .docs-page {
    width: 100%;
    min-height: auto;
  }
  
  .docs-page-content {
    padding: 0.5in;
  }
}

/* Print Styles */
@media print {
  .docs-header,
  .docs-toolbar,
  .docs-status-bar {
    display: none;
  }
  
  .docs-document-area {
    padding: 0;
    background: white;
  }
  
  .docs-page {
    box-shadow: none;
    margin: 0;
    page-break-after: always;
  }
}

/* Loading Animation */
.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #3018ef;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
