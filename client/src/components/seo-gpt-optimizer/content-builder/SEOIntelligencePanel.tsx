/**
 * SEO Intelligence Panel - Real-time SEO analysis and optimization
 * Provides live SEO scoring, suggestions, and content generation
 */

import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Brain, Target, TrendingUp, CheckCircle, AlertCircle, 
  Lightbulb, Wand2, Image, BarChart3, Eye, Sparkles
} from 'lucide-react';
import { seoIntelligenceService, SEOAnalysis } from '../../../services/seoIntelligenceService';

interface SEOIntelligencePanelProps {
  content: string;
  onContentGenerate: (content: string) => void;
  onImageGenerate: () => void;
  className?: string;
}

const SEOIntelligencePanel: React.FC<SEOIntelligencePanelProps> = ({
  content,
  onContentGenerate,
  onImageGenerate,
  className = ''
}) => {
  const [analysis, setAnalysis] = useState<SEOAnalysis | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [activeTab, setActiveTab] = useState<'analysis' | 'generate' | 'suggestions'>('analysis');

  // Analyze content when it changes (debounced)
  useEffect(() => {
    const timer = setTimeout(() => {
      if (content.trim()) {
        analyzeContent();
      }
    }, 1000);

    return () => clearTimeout(timer);
  }, [content]);

  const analyzeContent = useCallback(async () => {
    if (!content.trim()) return;

    setIsAnalyzing(true);
    try {
      const result = await seoIntelligenceService.analyzeContent(content);
      setAnalysis(result);
    } catch (error) {
      console.error('Failed to analyze content:', error);
    } finally {
      setIsAnalyzing(false);
    }
  }, [content]);

  const generateOptimizedContent = useCallback(async () => {
    setIsGenerating(true);
    try {
      const generatedContent = await seoIntelligenceService.generateContent({
        topic: 'SEO optimizado',
        keywords: analysis?.keywords.primary || ['SEO', 'contenido'],
        contentType: 'educational',
        targetLength: 800,
        tone: 'professional',
        includeImages: true
      });

      onContentGenerate(generatedContent.content);
    } catch (error) {
      console.error('Failed to generate content:', error);
    } finally {
      setIsGenerating(false);
    }
  }, [analysis, onContentGenerate]);

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBg = (score: number) => {
    if (score >= 80) return 'bg-green-100';
    if (score >= 60) return 'bg-yellow-100';
    return 'bg-red-100';
  };

  return (
    <div className={`bg-white rounded-xl shadow-lg border border-gray-200 ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200 bg-gradient-to-r from-[#3018ef]/5 to-[#dd3a5a]/5">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] rounded-lg">
            <Brain className="w-5 h-5 text-white" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900">Emma SEO Intelligence</h3>
            <p className="text-xs text-gray-600">Análisis en tiempo real</p>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex border-b border-gray-200">
        {[
          { id: 'analysis', label: 'Análisis', icon: BarChart3 },
          { id: 'generate', label: 'Generar', icon: Wand2 },
          { id: 'suggestions', label: 'Sugerencias', icon: Lightbulb }
        ].map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            className={`flex-1 flex items-center justify-center gap-2 py-3 px-4 text-sm font-medium transition-colors ${
              activeTab === tab.id
                ? 'text-[#3018ef] border-b-2 border-[#3018ef] bg-[#3018ef]/5'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <tab.icon className="w-4 h-4" />
            {tab.label}
          </button>
        ))}
      </div>

      {/* Content */}
      <div className="p-4 max-h-96 overflow-y-auto">
        <AnimatePresence mode="wait">
          {activeTab === 'analysis' && (
            <motion.div
              key="analysis"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="space-y-4"
            >
              {isAnalyzing ? (
                <div className="flex items-center justify-center py-8">
                  <div className="flex items-center gap-3">
                    <div className="w-5 h-5 border-2 border-[#3018ef] border-t-transparent rounded-full animate-spin"></div>
                    <span className="text-gray-600">Analizando contenido...</span>
                  </div>
                </div>
              ) : analysis ? (
                <>
                  {/* SEO Score */}
                  <div className={`p-4 rounded-lg ${getScoreBg(analysis.score)}`}>
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium text-gray-900">Puntuación SEO</h4>
                        <p className="text-sm text-gray-600">Optimización general</p>
                      </div>
                      <div className={`text-2xl font-bold ${getScoreColor(analysis.score)}`}>
                        {analysis.score}/100
                      </div>
                    </div>
                  </div>

                  {/* SAIO Score */}
                  <div className={`p-4 rounded-lg ${getScoreBg(analysis.saio.score)}`}>
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium text-gray-900">SAIO Score</h4>
                        <p className="text-sm text-gray-600">Optimización para IA</p>
                      </div>
                      <div className={`text-2xl font-bold ${getScoreColor(analysis.saio.score)}`}>
                        {analysis.saio.score}/100
                      </div>
                    </div>
                  </div>

                  {/* Quick Stats */}
                  <div className="grid grid-cols-2 gap-3">
                    <div className="p-3 bg-gray-50 rounded-lg">
                      <div className="text-sm text-gray-600">Palabras</div>
                      <div className="font-semibold text-gray-900">{analysis.structure.wordCount}</div>
                    </div>
                    <div className="p-3 bg-gray-50 rounded-lg">
                      <div className="text-sm text-gray-600">Legibilidad</div>
                      <div className="font-semibold text-gray-900">{analysis.readability.score}/100</div>
                    </div>
                  </div>

                  {/* Keywords */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Palabras Clave</h4>
                    <div className="flex flex-wrap gap-2">
                      {analysis.keywords.primary.map((keyword, index) => (
                        <span
                          key={index}
                          className="px-2 py-1 bg-[#3018ef]/10 text-[#3018ef] rounded-full text-xs font-medium"
                        >
                          {keyword}
                        </span>
                      ))}
                    </div>
                  </div>
                </>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <Eye className="w-8 h-8 mx-auto mb-2 opacity-50" />
                  <p>Escribe contenido para ver el análisis SEO</p>
                </div>
              )}
            </motion.div>
          )}

          {activeTab === 'generate' && (
            <motion.div
              key="generate"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="space-y-4"
            >
              <div className="text-center">
                <Sparkles className="w-12 h-12 mx-auto mb-3 text-[#3018ef]" />
                <h4 className="font-semibold text-gray-900 mb-2">Generación Inteligente</h4>
                <p className="text-sm text-gray-600 mb-6">
                  Crea contenido optimizado para SEO y SAIO automáticamente
                </p>
              </div>

              <div className="space-y-3">
                <button
                  onClick={generateOptimizedContent}
                  disabled={isGenerating}
                  className="w-full p-4 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] text-white rounded-lg hover:shadow-lg disabled:opacity-50 transition-all duration-200 flex items-center justify-center gap-3"
                >
                  {isGenerating ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      Generando...
                    </>
                  ) : (
                    <>
                      <Wand2 className="w-4 h-4" />
                      Generar Blog SEO Completo
                    </>
                  )}
                </button>

                <button
                  onClick={onImageGenerate}
                  className="w-full p-3 border-2 border-[#3018ef] text-[#3018ef] rounded-lg hover:bg-[#3018ef]/5 transition-all duration-200 flex items-center justify-center gap-2"
                >
                  <Image className="w-4 h-4" />
                  Generar Imágenes IA
                </button>
              </div>
            </motion.div>
          )}

          {activeTab === 'suggestions' && (
            <motion.div
              key="suggestions"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="space-y-3"
            >
              {analysis?.suggestions.length ? (
                analysis.suggestions.map((suggestion, index) => (
                  <div
                    key={index}
                    className="flex items-start gap-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg"
                  >
                    <Lightbulb className="w-4 h-4 text-yellow-600 mt-0.5 flex-shrink-0" />
                    <span className="text-sm text-yellow-800">{suggestion}</span>
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <CheckCircle className="w-8 h-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">¡Excelente! No hay sugerencias por ahora</p>
                </div>
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default SEOIntelligencePanel;
