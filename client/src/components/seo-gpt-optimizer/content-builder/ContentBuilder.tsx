/**
 * Emma Content Builder - Built on Etherpad Core
 * Real-time collaborative editor with <PERSON>'s branding and features
 */

import React, { useState, useCallback, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Save, CheckCircle, Image as ImageIcon, FileText,
  Bold, Italic, Underline, AlignLeft, AlignCenter, AlignRight, AlignJustify,
  List, ListOrdered, Minus, Link, Type, Palette, Wand2, Sparkles
} from 'lucide-react';

import EnhancedImageGenerator from './EnhancedImageGenerator';
import GoogleDocsEditor from './GoogleDocsEditor';
import SEOIntelligencePanel from './SEOIntelligencePanel';
import './EtherpadEditor.css';

interface ContentBuilderProps {
  projectId: string;
  initialContent?: string;
  initialTopic?: string;
  onContentChange?: (content: string) => void;
  className?: string;
}

const ContentBuilder: React.FC<ContentBuilderProps> = ({
  projectId,
  initialContent = '',
  initialTopic = '',
  onContentChange,
  className = ''
}) => {
  const [content, setContent] = useState(initialContent);
  const [title, setTitle] = useState(initialTopic || 'Documento sin título');
  const [showImageGenerator, setShowImageGenerator] = useState(false);
  const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'saved'>('idle');
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showSidebar, setShowSidebar] = useState(true);

  const editorRef = useRef<HTMLDivElement>(null);

  // Handle content changes with auto-save
  const handleContentChange = useCallback((newContent: string) => {
    setContent(newContent);

    if (onContentChange) {
      onContentChange(newContent);
    }
    // Auto-save with Emma branding
    setSaveStatus('saving');
    setTimeout(() => setSaveStatus('saved'), 1000);
  }, [onContentChange]);

  // Handle title changes
  const handleTitleChange = useCallback((newTitle: string) => {
    setTitle(newTitle);
  }, []);

  // Handle image insertion from Ideogram AI
  const handleImageInsert = useCallback((imageUrl: string, prompt: string) => {
    const imageHtml = `<div class="emma-image-container"><img src="${imageUrl}" alt="${prompt}" style="max-width: 100%; height: auto; border-radius: 8px; margin: 16px 0;" /><p class="emma-image-caption" style="font-size: 14px; color: #666; text-align: center; margin-top: 8px;">${prompt}</p></div>`;
    const newContent = content + imageHtml;
    handleContentChange(newContent);
    setShowImageGenerator(false);
  }, [content, handleContentChange]);

  // Simple stats calculation
  const wordCount = content.trim().split(/\s+/).filter(word => word.length > 0).length;
  const charCount = content.length;



  return (
    <div className="h-screen flex">
      {/* Google Docs Editor - Full Width */}
      <div className="flex-1">
        <GoogleDocsEditor
          content={content}
          onChange={handleContentChange}
          onImageInsert={() => setShowImageGenerator(true)}
          projectId={projectId}
          documentTitle={title}
          onTitleChange={handleTitleChange}
          collaborators={[
            {
              id: 'emma-ai',
              name: 'Emma AI',
              color: '#3018ef'
            }
          ]}
          className="h-full"
          showSidebar={showSidebar}
          onToggleSidebar={() => setShowSidebar(!showSidebar)}
        />
      </div>

      {/* SEO Intelligence Sidebar - Conditional */}
      {showSidebar && (
        <div className="w-80 flex-shrink-0 border-l border-gray-200 bg-white">
          <SEOIntelligencePanel
            content={content}
            onContentGenerate={handleContentChange}
            onImageGenerate={() => setShowImageGenerator(true)}
            className="h-full"
          />
        </div>
      )}

      {/* Image Generator Modal */}
      <AnimatePresence>
        {showImageGenerator && (
          <EnhancedImageGenerator
            onImageInsert={handleImageInsert}
            onClose={() => setShowImageGenerator(false)}
          />
        )}
      </AnimatePresence>
    </div>
  );
};

export default ContentBuilder;
