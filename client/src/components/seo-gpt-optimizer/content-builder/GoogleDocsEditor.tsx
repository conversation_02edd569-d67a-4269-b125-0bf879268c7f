/**
 * GoogleDocsEditor - Real Google Docs-style editor powered by Etherpad
 * Professional interface with Emma branding and real-time collaboration
 */

import React, { useEffect, useRef, useState, useCallback } from 'react';
import { motion } from 'framer-motion';
import {
  Bold, Italic, Underline, AlignLeft, AlignCenter, AlignRight,
  List, ListOrdered, Link, Type, Image, Wand2, Sparkles,
  MoreHorizontal, Undo, Redo, Printer, Share, Users, MessageCircle,
  History, Star, Folder, ChevronDown, Highlighter, PanelRightOpen, PanelRightClose
} from 'lucide-react';
// import { etherpadService } from '../../../services/etherpadService'; // DISABLED - no external services

interface GoogleDocsEditorProps {
  content: string;
  onChange: (content: string) => void;
  onImageInsert?: () => void;
  projectId: string;
  className?: string;
  documentTitle?: string;
  onTitleChange?: (title: string) => void;
  collaborators?: Array<{
    id: string;
    name: string;
    avatar?: string;
    color: string;
  }>;
  showSidebar?: boolean;
  onToggleSidebar?: () => void;
}

const GoogleDocsEditor: React.FC<GoogleDocsEditorProps> = ({
  content,
  onChange,
  onImageInsert,
  projectId,
  className = '',
  documentTitle = 'Documento sin título',
  onTitleChange,
  collaborators = [],
  showSidebar = true,
  onToggleSidebar
}) => {
  const editorRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLInputElement>(null);
  
  // State management
  const [isReady, setIsReady] = useState(false);
  const [padId, setPadId] = useState<string>('');
  const [isConnected, setIsConnected] = useState(false);
  const [currentTitle, setCurrentTitle] = useState(documentTitle);
  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date>(new Date());
  const [isSaving, setIsSaving] = useState(false);
  const [wordCount, setWordCount] = useState(0);
  const [zoomLevel, setZoomLevel] = useState(100);

  // Initialize simple editor - NO external services
  useEffect(() => {
    if (editorRef.current) {
      editorRef.current.innerHTML = '';

      // Create simple contenteditable div that looks like Google Docs
      const editorDiv = document.createElement('div');
      editorDiv.contentEditable = 'true';
      editorDiv.style.cssText = `
        width: 100%;
        height: 100%;
        font-family: 'Arial', sans-serif;
        font-size: 11pt;
        line-height: 1.5;
        color: #202124;
        outline: none;
        padding: 0;
        margin: 0;
        background: white;
        min-height: 100%;
      `;

      // Set initial content
      editorDiv.innerHTML = content || '<p>Comienza a escribir tu documento...</p>';

      // Add event listeners
      editorDiv.addEventListener('input', (e) => {
        const newContent = editorDiv.innerHTML;
        onChange(newContent);
        updateWordCount(newContent);
      });

      editorDiv.addEventListener('keydown', (e) => {
        // Handle keyboard shortcuts
        if (e.ctrlKey || e.metaKey) {
          switch (e.key) {
            case 'b':
              e.preventDefault();
              document.execCommand('bold');
              break;
            case 'i':
              e.preventDefault();
              document.execCommand('italic');
              break;
            case 'u':
              e.preventDefault();
              document.execCommand('underline');
              break;
          }
        }
      });

      editorRef.current.appendChild(editorDiv);
      setIsReady(true);
      setIsConnected(true); // Fake connection status
    }
  }, [projectId]);

  // Update word count
  const updateWordCount = useCallback((text: string) => {
    const words = text.replace(/<[^>]*>/g, '').trim().split(/\s+/).filter(word => word.length > 0);
    setWordCount(words.length);
  }, []);

  // Auto-save functionality
  useEffect(() => {
    if (content) {
      updateWordCount(content);
    }
  }, [content, updateWordCount]);

  return (
    <div className={`google-docs-editor ${className}`} style={{ height: '100vh', display: 'flex', flexDirection: 'column', background: '#f8f9fa' }}>
      {/* Compact Google Docs Header */}
      <div style={{ background: 'white', borderBottom: '1px solid #e8eaed' }}>
        {/* Top Menu Bar - Compact */}
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '4px 16px', minHeight: '40px' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            {/* Emma Logo - Smaller */}
            <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
              <div style={{
                width: '24px',
                height: '24px',
                background: 'linear-gradient(135deg, #3018ef 0%, #dd3a5a 100%)',
                borderRadius: '6px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <Sparkles size={12} color="white" />
              </div>
              <span style={{ fontWeight: '600', color: '#202124', fontSize: '14px' }}>Emma Docs</span>
            </div>

            {/* Document Title - Inline */}
            <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
              {isEditingTitle ? (
                <input
                  ref={titleRef}
                  value={currentTitle}
                  onChange={(e) => setCurrentTitle(e.target.value)}
                  onBlur={() => {
                    setIsEditingTitle(false);
                    onTitleChange?.(currentTitle);
                  }}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      setIsEditingTitle(false);
                      onTitleChange?.(currentTitle);
                    }
                  }}
                  style={{
                    fontSize: '14px',
                    fontWeight: '400',
                    color: '#202124',
                    background: 'transparent',
                    border: 'none',
                    borderBottom: '1px solid #3018ef',
                    outline: 'none',
                    padding: '2px 4px',
                    minWidth: '200px'
                  }}
                  autoFocus
                />
              ) : (
                <span
                  onClick={() => setIsEditingTitle(true)}
                  style={{
                    fontSize: '14px',
                    fontWeight: '400',
                    color: '#202124',
                    cursor: 'pointer',
                    padding: '2px 4px',
                    borderRadius: '3px',
                    transition: 'background 0.2s'
                  }}
                  onMouseEnter={(e) => e.currentTarget.style.background = '#f1f3f4'}
                  onMouseLeave={(e) => e.currentTarget.style.background = 'transparent'}
                >
                  {currentTitle}
                </span>
              )}
              <Star size={14} style={{ color: '#9aa0a6', cursor: 'pointer' }} />
            </div>
          </div>

          {/* Right Side Actions - Compact */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
            {/* Collaborators */}
            {collaborators.length > 0 && (
              <div style={{ display: 'flex', alignItems: 'center', gap: '2px' }}>
                {collaborators.slice(0, 2).map((collaborator) => (
                  <div
                    key={collaborator.id}
                    style={{
                      width: '24px',
                      height: '24px',
                      borderRadius: '50%',
                      background: collaborator.color,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      color: 'white',
                      fontSize: '10px',
                      fontWeight: '500'
                    }}
                    title={collaborator.name}
                  >
                    {collaborator.name.charAt(0).toUpperCase()}
                  </div>
                ))}
              </div>
            )}

            {/* Compact Share Button */}
            <button style={{
              display: 'flex',
              alignItems: 'center',
              gap: '4px',
              padding: '6px 12px',
              border: 'none',
              borderRadius: '4px',
              background: 'linear-gradient(135deg, #3018ef 0%, #dd3a5a 100%)',
              color: 'white',
              cursor: 'pointer',
              fontSize: '12px',
              fontWeight: '500'
            }}>
              <Share size={14} />
              <span>Compartir</span>
            </button>
          </div>
        </div>

        {/* Compact Menu Navigation */}
        <div style={{ padding: '0 16px', borderTop: '1px solid #e8eaed' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
            {['Archivo', 'Editar', 'Ver', 'Insertar', 'Formato', 'Herramientas', 'Emma AI'].map((item) => (
              <button
                key={item}
                style={{
                  padding: '6px 8px',
                  border: 'none',
                  background: 'transparent',
                  color: '#5f6368',
                  fontSize: '13px',
                  cursor: 'pointer',
                  borderRadius: '3px'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.background = '#f1f3f4';
                  e.currentTarget.style.color = '#202124';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.background = 'transparent';
                  e.currentTarget.style.color = '#5f6368';
                }}
              >
                {item}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Compact Google Docs Toolbar */}
      <div style={{ background: 'white', borderBottom: '1px solid #e8eaed', padding: '4px 16px' }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '6px', flexWrap: 'wrap' }}>
          {/* Undo/Redo/Print */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '1px' }}>
            <button
              onClick={() => document.execCommand('undo')}
              style={{ width: '28px', height: '28px', border: 'none', borderRadius: '3px', background: 'transparent', cursor: 'pointer', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
              onMouseEnter={(e) => e.currentTarget.style.background = '#f1f3f4'}
              onMouseLeave={(e) => e.currentTarget.style.background = 'transparent'}
              title="Deshacer"
            >
              <Undo size={14} color="#5f6368" />
            </button>
            <button
              onClick={() => document.execCommand('redo')}
              style={{ width: '28px', height: '28px', border: 'none', borderRadius: '3px', background: 'transparent', cursor: 'pointer', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
              onMouseEnter={(e) => e.currentTarget.style.background = '#f1f3f4'}
              onMouseLeave={(e) => e.currentTarget.style.background = 'transparent'}
              title="Rehacer"
            >
              <Redo size={14} color="#5f6368" />
            </button>
            <button
              onClick={() => window.print()}
              style={{ width: '28px', height: '28px', border: 'none', borderRadius: '3px', background: 'transparent', cursor: 'pointer', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
              onMouseEnter={(e) => e.currentTarget.style.background = '#f1f3f4'}
              onMouseLeave={(e) => e.currentTarget.style.background = 'transparent'}
              title="Imprimir"
            >
              <Printer size={14} color="#5f6368" />
            </button>
          </div>

          <div style={{ width: '1px', height: '20px', background: '#e8eaed', margin: '0 3px' }} />

          {/* Zoom */}
          <select
            value={zoomLevel}
            onChange={(e) => setZoomLevel(Number(e.target.value))}
            style={{
              border: '1px solid #dadce0',
              borderRadius: '3px',
              padding: '2px 6px',
              fontSize: '12px',
              background: '#ffffff',
              color: '#202124',
              minWidth: '60px',
              opacity: 1
            }}
          >
            <option value={75}>75%</option>
            <option value={100}>100%</option>
            <option value={125}>125%</option>
            <option value={150}>150%</option>
          </select>

          <div style={{ width: '1px', height: '20px', background: '#e8eaed', margin: '0 3px' }} />

          {/* Font Controls */}
          <select
            onChange={(e) => document.execCommand('fontName', false, e.target.value)}
            style={{
              border: '1px solid #dadce0',
              borderRadius: '3px',
              padding: '2px 6px',
              fontSize: '12px',
              background: '#ffffff',
              color: '#202124',
              minWidth: '100px',
              opacity: 1
            }}
          >
            <option value="Arial">Arial</option>
            <option value="Calibri">Calibri</option>
            <option value="Times New Roman">Times New Roman</option>
            <option value="Georgia">Georgia</option>
          </select>

          <select
            onChange={(e) => document.execCommand('fontSize', false, e.target.value)}
            style={{
              border: '1px solid #dadce0',
              borderRadius: '3px',
              padding: '2px 6px',
              fontSize: '12px',
              background: '#ffffff',
              color: '#202124',
              minWidth: '50px',
              opacity: 1
            }}
          >
            <option value="1">8</option>
            <option value="2">10</option>
            <option value="3">12</option>
            <option value="4">14</option>
            <option value="5">18</option>
            <option value="6">24</option>
            <option value="7">36</option>
          </select>

          <div style={{ width: '1px', height: '20px', background: '#e8eaed', margin: '0 3px' }} />

          {/* Formatting Buttons */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '1px' }}>
            <button
              onClick={() => document.execCommand('bold')}
              style={{ width: '28px', height: '28px', border: 'none', borderRadius: '3px', background: 'transparent', cursor: 'pointer', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
              onMouseEnter={(e) => e.currentTarget.style.background = '#f1f3f4'}
              onMouseLeave={(e) => e.currentTarget.style.background = 'transparent'}
              title="Negrita"
            >
              <Bold size={14} color="#5f6368" />
            </button>
            <button
              onClick={() => document.execCommand('italic')}
              style={{ width: '28px', height: '28px', border: 'none', borderRadius: '3px', background: 'transparent', cursor: 'pointer', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
              onMouseEnter={(e) => e.currentTarget.style.background = '#f1f3f4'}
              onMouseLeave={(e) => e.currentTarget.style.background = 'transparent'}
              title="Cursiva"
            >
              <Italic size={14} color="#5f6368" />
            </button>
            <button
              onClick={() => document.execCommand('underline')}
              style={{ width: '28px', height: '28px', border: 'none', borderRadius: '3px', background: 'transparent', cursor: 'pointer', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
              onMouseEnter={(e) => e.currentTarget.style.background = '#f1f3f4'}
              onMouseLeave={(e) => e.currentTarget.style.background = 'transparent'}
              title="Subrayado"
            >
              <Underline size={14} color="#5f6368" />
            </button>
          </div>

          <div style={{ width: '1px', height: '20px', background: '#e8eaed', margin: '0 3px' }} />

          {/* Alignment */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '1px' }}>
            <button
              onClick={() => document.execCommand('justifyLeft')}
              style={{ width: '28px', height: '28px', border: 'none', borderRadius: '3px', background: 'transparent', cursor: 'pointer', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
              onMouseEnter={(e) => e.currentTarget.style.background = '#f1f3f4'}
              onMouseLeave={(e) => e.currentTarget.style.background = 'transparent'}
              title="Alinear izquierda"
            >
              <AlignLeft size={14} color="#5f6368" />
            </button>
            <button
              onClick={() => document.execCommand('justifyCenter')}
              style={{ width: '28px', height: '28px', border: 'none', borderRadius: '3px', background: 'transparent', cursor: 'pointer', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
              onMouseEnter={(e) => e.currentTarget.style.background = '#f1f3f4'}
              onMouseLeave={(e) => e.currentTarget.style.background = 'transparent'}
              title="Centrar"
            >
              <AlignCenter size={14} color="#5f6368" />
            </button>
            <button
              onClick={() => document.execCommand('justifyRight')}
              style={{ width: '28px', height: '28px', border: 'none', borderRadius: '3px', background: 'transparent', cursor: 'pointer', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
              onMouseEnter={(e) => e.currentTarget.style.background = '#f1f3f4'}
              onMouseLeave={(e) => e.currentTarget.style.background = 'transparent'}
              title="Alinear derecha"
            >
              <AlignRight size={14} color="#5f6368" />
            </button>
          </div>

          <div style={{ width: '1px', height: '20px', background: '#e8eaed', margin: '0 3px' }} />

          {/* Lists */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '1px' }}>
            <button
              onClick={() => document.execCommand('insertUnorderedList')}
              style={{ width: '28px', height: '28px', border: 'none', borderRadius: '3px', background: 'transparent', cursor: 'pointer', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
              onMouseEnter={(e) => e.currentTarget.style.background = '#f1f3f4'}
              onMouseLeave={(e) => e.currentTarget.style.background = 'transparent'}
              title="Lista con viñetas"
            >
              <List size={14} color="#5f6368" />
            </button>
            <button
              onClick={() => document.execCommand('insertOrderedList')}
              style={{ width: '28px', height: '28px', border: 'none', borderRadius: '3px', background: 'transparent', cursor: 'pointer', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
              onMouseEnter={(e) => e.currentTarget.style.background = '#f1f3f4'}
              onMouseLeave={(e) => e.currentTarget.style.background = 'transparent'}
              title="Lista numerada"
            >
              <ListOrdered size={14} color="#5f6368" />
            </button>
          </div>

          <div style={{ width: '1px', height: '20px', background: '#e8eaed', margin: '0 3px' }} />

          {/* Sidebar Toggle Button - Bigger and More Visible */}
          {onToggleSidebar && (
            <button
              onClick={onToggleSidebar}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '6px',
                padding: '6px 12px',
                border: '1px solid #dadce0',
                borderRadius: '4px',
                background: showSidebar ? '#e8f0fe' : '#ffffff',
                color: showSidebar ? '#1a73e8' : '#5f6368',
                cursor: 'pointer',
                fontSize: '13px',
                fontWeight: '500',
                transition: 'all 0.2s ease'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.background = showSidebar ? '#d2e3fc' : '#f8f9fa';
                e.currentTarget.style.borderColor = '#1a73e8';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.background = showSidebar ? '#e8f0fe' : '#ffffff';
                e.currentTarget.style.borderColor = '#dadce0';
              }}
              title={showSidebar ? 'Ocultar Emma SEO Intelligence' : 'Mostrar Emma SEO Intelligence'}
            >
              {showSidebar ? <PanelRightClose size={16} /> : <PanelRightOpen size={16} />}
              <span>{showSidebar ? 'Ocultar SEO' : 'Emma SEO'}</span>
            </button>
          )}

          {/* Emma AI Button */}
          {onImageInsert && (
            <button
              onClick={onImageInsert}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '3px',
                padding: '4px 8px',
                border: 'none',
                borderRadius: '4px',
                background: 'linear-gradient(135deg, #3018ef 0%, #dd3a5a 100%)',
                color: 'white',
                cursor: 'pointer',
                fontSize: '12px',
                fontWeight: '500'
              }}
              title="Generar imagen con Emma AI"
            >
              <Image size={12} />
              <Wand2 size={10} />
              <span>Emma AI</span>
            </button>
          )}
        </div>
      </div>

      {/* Document Area */}
      <div style={{ flex: 1, overflow: 'auto', background: '#f8f9fa', padding: '20px', display: 'flex', justifyContent: 'center' }}>
        <div style={{ transform: `scale(${zoomLevel / 100})`, transformOrigin: 'top center' }}>
          <div 
            ref={editorRef}
            style={{
              width: '8.5in',
              minHeight: '11in',
              background: 'white',
              boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
              borderRadius: '2px'
            }}
          />
        </div>
      </div>

      {/* Status Bar */}
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center', 
        padding: '8px 16px', 
        background: 'white', 
        borderTop: '1px solid #e8eaed', 
        fontSize: '13px', 
        color: '#5f6368' 
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
          {/* Save Status */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            {isSaving ? (
              <>
                <div style={{ width: '12px', height: '12px', border: '2px solid #3018ef', borderTop: '2px solid transparent', borderRadius: '50%', animation: 'spin 1s linear infinite' }} />
                <span>Guardando...</span>
              </>
            ) : (
              <>
                <div style={{ width: '8px', height: '8px', background: '#34a853', borderRadius: '50%' }} />
                <span>Guardado {lastSaved.toLocaleTimeString()}</span>
              </>
            )}
          </div>

          <span>{wordCount} palabras</span>

          {isConnected ? (
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <div style={{ width: '8px', height: '8px', background: '#34a853', borderRadius: '50%' }} />
              <span style={{ color: '#34a853' }}>Colaboración activa</span>
            </div>
          ) : (
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <div style={{ width: '8px', height: '8px', background: '#ea4335', borderRadius: '50%' }} />
              <span style={{ color: '#ea4335' }}>Sin conexión</span>
            </div>
          )}
        </div>

        {/* Emma AI Indicator */}
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <Sparkles size={14} color="#3018ef" />
          <span style={{ 
            background: 'linear-gradient(135deg, #3018ef 0%, #dd3a5a 100%)', 
            WebkitBackgroundClip: 'text', 
            WebkitTextFillColor: 'transparent',
            fontWeight: '500'
          }}>
            Emma AI
          </span>
        </div>
      </div>
    </div>
  );
};

export default GoogleDocsEditor;
export { GoogleDocsEditor as EtherpadEditor };
