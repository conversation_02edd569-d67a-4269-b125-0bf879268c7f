/* <PERSON> Editor Styles */
.emma-etherpad-editor {
  @apply w-full h-full;
}

/* Too<PERSON><PERSON> Styles with <PERSON> */
.emma-editor-toolbar {
  @apply flex items-center gap-2 p-3 bg-white border-b border-gray-200 rounded-t-xl;
  background: linear-gradient(135deg, rgba(48, 24, 239, 0.05) 0%, rgba(221, 58, 90, 0.05) 100%);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.toolbar-section {
  @apply flex items-center gap-1;
}

.toolbar-divider {
  @apply w-px h-6 bg-gray-300 mx-2;
}

.toolbar-btn {
  @apply flex items-center justify-center w-8 h-8 rounded-lg border-none bg-transparent hover:bg-gray-100 transition-all duration-200 cursor-pointer;
  color: #4a5568;
}

.toolbar-btn:hover {
  @apply bg-gray-100 transform scale-105;
  color: #3018ef;
}

.toolbar-btn.emma-gradient {
  background: linear-gradient(135deg, #3018ef 0%, #dd3a5a 100%);
  @apply text-white hover:shadow-lg;
}

.toolbar-btn.emma-gradient:hover {
  @apply bg-none transform scale-105;
  box-shadow: 0 8px 25px rgba(48, 24, 239, 0.3);
}

/* Editor Container */
.emma-editor-wrapper {
  @apply relative overflow-hidden;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.emma-editor-container {
  @apply w-full h-full relative;
}

/* Loading Styles */
.emma-loading {
  @apply absolute inset-0 flex flex-col items-center justify-center bg-white z-10;
  background: linear-gradient(135deg, rgba(48, 24, 239, 0.05) 0%, rgba(221, 58, 90, 0.05) 100%);
}

.loading-spinner {
  @apply w-8 h-8 border-4 border-gray-200 border-t-[#3018ef] rounded-full animate-spin mb-4;
}

.emma-loading p {
  @apply text-gray-600 font-medium;
}

/* Content Editor Styles */
.emma-editor-content {
  @apply w-full h-full p-8 outline-none;
  min-height: 500px;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 16px;
  line-height: 1.6;
  color: #2d3748;
}

.emma-editor-content:focus {
  @apply outline-none;
}

.emma-editor-content p {
  @apply mb-4;
}

.emma-editor-content h1,
.emma-editor-content h2,
.emma-editor-content h3,
.emma-editor-content h4,
.emma-editor-content h5,
.emma-editor-content h6 {
  @apply font-bold mb-4 mt-6;
  color: #1a202c;
}

.emma-editor-content h1 {
  @apply text-3xl;
}

.emma-editor-content h2 {
  @apply text-2xl;
}

.emma-editor-content h3 {
  @apply text-xl;
}

.emma-editor-content ul,
.emma-editor-content ol {
  @apply ml-6 mb-4;
}

.emma-editor-content li {
  @apply mb-2;
}

.emma-editor-content blockquote {
  @apply border-l-4 border-[#3018ef] pl-4 italic text-gray-700 my-4;
  background: linear-gradient(135deg, rgba(48, 24, 239, 0.05) 0%, rgba(221, 58, 90, 0.05) 100%);
  padding: 16px;
  border-radius: 8px;
}

.emma-editor-content a {
  @apply text-[#3018ef] hover:text-[#dd3a5a] underline transition-colors duration-200;
}

.emma-editor-content strong {
  @apply font-bold;
  color: #1a202c;
}

.emma-editor-content em {
  @apply italic;
}

.emma-editor-content code {
  @apply bg-gray-100 px-2 py-1 rounded text-sm font-mono;
  color: #e53e3e;
}

.emma-editor-content pre {
  @apply bg-gray-100 p-4 rounded-lg overflow-x-auto mb-4;
}

.emma-editor-content pre code {
  @apply bg-transparent p-0;
}

/* Status Bar */
.emma-editor-status {
  @apply flex items-center justify-between p-3 bg-gray-50 border-t border-gray-200 rounded-b-xl text-sm;
}

.status-text {
  @apply text-gray-600;
}

.emma-brand-indicator {
  @apply flex items-center gap-2;
}

/* Selection Styles */
.emma-editor-content ::selection {
  background: rgba(48, 24, 239, 0.2);
}

.emma-editor-content ::-moz-selection {
  background: rgba(48, 24, 239, 0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
  .emma-editor-toolbar {
    @apply flex-wrap gap-1 p-2;
  }
  
  .toolbar-section {
    @apply gap-0.5;
  }
  
  .toolbar-btn {
    @apply w-7 h-7;
  }
  
  .emma-editor-content {
    @apply p-4 text-sm;
  }
}

/* Enhanced Glassmorphism Effects */
.emma-editor-wrapper::before {
  content: '';
  @apply absolute inset-0 rounded-xl;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  backdrop-filter: blur(10px);
  z-index: -1;
}

.emma-editor-wrapper::after {
  content: '';
  @apply absolute inset-0 rounded-xl pointer-events-none;
  background: linear-gradient(135deg, rgba(48, 24, 239, 0.03) 0%, rgba(221, 58, 90, 0.03) 100%);
  z-index: 1;
}

/* Emma Brand Gradient Animations */
@keyframes emmaGradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.emma-gradient-animated {
  background: linear-gradient(-45deg, #3018ef, #dd3a5a, #3018ef, #dd3a5a);
  background-size: 400% 400%;
  animation: emmaGradient 3s ease infinite;
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Focus States */
.emma-editor-content:focus-within {
  box-shadow: 0 0 0 3px rgba(48, 24, 239, 0.1);
}

/* Placeholder Styles */
.emma-editor-content:empty::before {
  content: 'Comienza a escribir tu contenido...';
  @apply text-gray-400 pointer-events-none;
}

/* Custom Scrollbar */
.emma-editor-content::-webkit-scrollbar {
  width: 8px;
}

.emma-editor-content::-webkit-scrollbar-track {
  @apply bg-gray-100 rounded;
}

.emma-editor-content::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #3018ef 0%, #dd3a5a 100%);
  @apply rounded;
}

.emma-editor-content::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #2614d4 0%, #c23350 100%);
}

/* Emma Brand Enhancements */
.emma-brand-glow {
  box-shadow: 0 0 20px rgba(48, 24, 239, 0.3), 0 0 40px rgba(221, 58, 90, 0.2);
}

.emma-text-gradient {
  background: linear-gradient(135deg, #3018ef 0%, #dd3a5a 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Connection Status Indicator */
.connection-indicator {
  @apply flex items-center gap-2 px-3 py-1 rounded-full;
  background: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.2);
  backdrop-filter: blur(10px);
}

.connection-indicator.disconnected {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

/* Enhanced Toolbar Glassmorphism */
.emma-editor-toolbar {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Floating Action Button Style */
.emma-floating-btn {
  @apply fixed bottom-6 right-6 w-14 h-14 rounded-full flex items-center justify-center text-white shadow-lg;
  background: linear-gradient(135deg, #3018ef 0%, #dd3a5a 100%);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.emma-floating-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 12px 40px rgba(48, 24, 239, 0.4);
}

/* Status Bar Enhancements */
.emma-editor-status {
  background: rgba(249, 250, 251, 0.8);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(255, 255, 255, 0.3);
}
