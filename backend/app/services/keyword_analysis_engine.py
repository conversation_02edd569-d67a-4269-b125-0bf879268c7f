"""
Real Keyword Analysis Engine for Emma Studio
Implements TF-IDF based keyword analysis, semantic keyword detection, and competitive scoring
"""

import re
import math
import logging
from typing import Dict, List, Tuple, Set
from collections import Counter, defaultdict
from dataclasses import dataclass
import nltk
from nltk.corpus import stopwords
from nltk.tokenize import word_tokenize
from nltk.stem import SnowballStemmer

logger = logging.getLogger(__name__)

@dataclass
class KeywordAnalysisResult:
    """Keyword analysis results with competitive scoring"""
    primary_keywords: List[Dict[str, any]]
    secondary_keywords: List[Dict[str, any]]
    semantic_keywords: List[Dict[str, any]]
    keyword_density: Dict[str, float]
    tf_idf_scores: Dict[str, float]
    keyword_distribution: Dict[str, List[int]]  # positions in text
    competitive_score: float
    optimization_suggestions: List[str]

class RealKeywordAnalysisEngine:
    """
    Real Keyword Analysis Engine using proven algorithms
    Implements TF-IDF, semantic analysis, and competitive keyword scoring
    """
    
    def __init__(self):
        # Initialize NLTK components
        try:
            self.stop_words_es = set(stopwords.words('spanish'))
            self.stop_words_en = set(stopwords.words('english'))
            self.stemmer_es = SnowballStemmer('spanish')
            self.stemmer_en = SnowballStemmer('english')
        except LookupError:
            logger.warning("NLTK data not found, downloading...")
            nltk.download('stopwords')
            nltk.download('punkt')
            self.stop_words_es = set(stopwords.words('spanish'))
            self.stop_words_en = set(stopwords.words('english'))
            self.stemmer_es = SnowballStemmer('spanish')
            self.stemmer_en = SnowballStemmer('english')
        
        self.stop_words = self.stop_words_es.union(self.stop_words_en)
        
        # Optimal keyword density ranges (proven SEO data)
        self.optimal_density = {
            'primary': (1.0, 3.0),    # 1-3% for primary keywords
            'secondary': (0.5, 2.0),  # 0.5-2% for secondary keywords
            'long_tail': (0.1, 1.0)   # 0.1-1% for long-tail keywords
        }
    
    def analyze_keywords(self, content: str, target_keywords: List[str] = None, 
                        language: str = 'es') -> KeywordAnalysisResult:
        """
        Perform comprehensive keyword analysis using proven algorithms
        
        Args:
            content: Text content to analyze
            target_keywords: List of target keywords to analyze
            language: Content language ('es' or 'en')
            
        Returns:
            KeywordAnalysisResult with detailed keyword metrics
        """
        try:
            # Clean and tokenize content
            clean_text = self._clean_text(content)
            tokens = self._tokenize_text(clean_text, language)
            
            # Extract keywords from content
            extracted_keywords = self._extract_keywords(tokens, language)
            
            # Combine target keywords with extracted keywords
            all_keywords = list(set((target_keywords or []) + extracted_keywords))
            
            # Calculate keyword metrics
            keyword_density = self._calculate_keyword_density(clean_text, all_keywords)
            tf_idf_scores = self._calculate_tf_idf_scores(tokens, all_keywords, language)
            keyword_positions = self._find_keyword_positions(clean_text, all_keywords)
            
            # Classify keywords
            primary_keywords = self._classify_primary_keywords(
                all_keywords, keyword_density, tf_idf_scores, target_keywords or []
            )
            secondary_keywords = self._classify_secondary_keywords(
                all_keywords, keyword_density, tf_idf_scores, primary_keywords
            )
            semantic_keywords = self._find_semantic_keywords(
                tokens, primary_keywords + secondary_keywords, language
            )
            
            # Calculate competitive score
            competitive_score = self._calculate_competitive_score(
                primary_keywords, secondary_keywords, keyword_density
            )
            
            # Generate optimization suggestions
            suggestions = self._generate_optimization_suggestions(
                primary_keywords, secondary_keywords, keyword_density, keyword_positions
            )
            
            return KeywordAnalysisResult(
                primary_keywords=primary_keywords,
                secondary_keywords=secondary_keywords,
                semantic_keywords=semantic_keywords,
                keyword_density=keyword_density,
                tf_idf_scores=tf_idf_scores,
                keyword_distribution=keyword_positions,
                competitive_score=competitive_score,
                optimization_suggestions=suggestions
            )
            
        except Exception as e:
            logger.error(f"Keyword analysis failed: {str(e)}")
            raise
    
    def _clean_text(self, content: str) -> str:
        """Clean HTML and normalize text"""
        # Remove HTML tags
        clean_text = re.sub(r'<[^>]+>', ' ', content)
        # Normalize whitespace
        clean_text = re.sub(r'\s+', ' ', clean_text)
        # Remove special characters but keep accents
        clean_text = re.sub(r'[^\w\sáéíóúñüÁÉÍÓÚÑÜ]', ' ', clean_text)
        return clean_text.strip().lower()
    
    def _tokenize_text(self, text: str, language: str) -> List[str]:
        """Tokenize text and remove stop words"""
        tokens = word_tokenize(text)
        stop_words = self.stop_words_es if language == 'es' else self.stop_words_en
        
        # Filter tokens: remove stop words, short words, numbers
        filtered_tokens = [
            token for token in tokens 
            if (token.lower() not in stop_words and 
                len(token) > 2 and 
                token.isalpha())
        ]
        
        return filtered_tokens
    
    def _extract_keywords(self, tokens: List[str], language: str, top_n: int = 20) -> List[str]:
        """Extract potential keywords using frequency analysis"""
        # Count token frequencies
        token_freq = Counter(tokens)
        
        # Extract n-grams (2-3 word phrases)
        bigrams = self._extract_ngrams(tokens, 2)
        trigrams = self._extract_ngrams(tokens, 3)
        
        # Combine single words and phrases
        all_candidates = []
        
        # Single words (top frequency)
        for word, freq in token_freq.most_common(top_n):
            if freq >= 2:  # Minimum frequency threshold
                all_candidates.append(word)
        
        # Bigrams and trigrams
        all_candidates.extend(bigrams[:10])  # Top 10 bigrams
        all_candidates.extend(trigrams[:5])   # Top 5 trigrams
        
        return list(set(all_candidates))
    
    def _extract_ngrams(self, tokens: List[str], n: int) -> List[str]:
        """Extract n-grams from tokens"""
        ngrams = []
        for i in range(len(tokens) - n + 1):
            ngram = ' '.join(tokens[i:i+n])
            ngrams.append(ngram)
        
        # Count and return most frequent n-grams
        ngram_freq = Counter(ngrams)
        return [ngram for ngram, freq in ngram_freq.most_common(10) if freq >= 2]
    
    def _calculate_keyword_density(self, text: str, keywords: List[str]) -> Dict[str, float]:
        """Calculate keyword density using proven SEO formula"""
        words = text.split()
        total_words = len(words)
        
        if total_words == 0:
            return {}
        
        density = {}
        for keyword in keywords:
            # Count exact matches and partial matches
            keyword_lower = keyword.lower()
            count = text.lower().count(keyword_lower)
            
            # Calculate density as percentage
            density[keyword] = (count / total_words) * 100
        
        return density
    
    def _calculate_tf_idf_scores(self, tokens: List[str], keywords: List[str], 
                                language: str) -> Dict[str, float]:
        """Calculate TF-IDF scores for keywords"""
        # Term frequency
        token_count = Counter(tokens)
        total_tokens = len(tokens)
        
        tf_idf_scores = {}
        
        for keyword in keywords:
            # Handle multi-word keywords
            keyword_tokens = keyword.lower().split()
            
            if len(keyword_tokens) == 1:
                # Single word
                tf = token_count.get(keyword_tokens[0], 0) / total_tokens
            else:
                # Multi-word: average TF of component words
                tf_sum = sum(token_count.get(token, 0) for token in keyword_tokens)
                tf = tf_sum / (total_tokens * len(keyword_tokens))
            
            # Simplified IDF (in production, use large corpus)
            # Higher score for less common words
            idf = math.log(1000 / (token_count.get(keyword_tokens[0], 0) + 1))
            
            tf_idf_scores[keyword] = tf * idf
        
        return tf_idf_scores
    
    def _find_keyword_positions(self, text: str, keywords: List[str]) -> Dict[str, List[int]]:
        """Find positions of keywords in text"""
        positions = defaultdict(list)
        words = text.split()
        
        for keyword in keywords:
            keyword_lower = keyword.lower()
            
            # Find all occurrences
            for i, word in enumerate(words):
                if keyword_lower in word.lower():
                    positions[keyword].append(i)
        
        return dict(positions)
    
    def _classify_primary_keywords(self, keywords: List[str], density: Dict[str, float],
                                  tf_idf: Dict[str, float], target_keywords: List[str]) -> List[Dict[str, any]]:
        """Classify primary keywords based on density and relevance"""
        primary = []
        
        for keyword in keywords:
            kw_density = density.get(keyword, 0)
            kw_tf_idf = tf_idf.get(keyword, 0)
            is_target = keyword in target_keywords
            
            # Primary keyword criteria
            if (is_target or 
                kw_density >= self.optimal_density['primary'][0] or
                kw_tf_idf > 0.01):
                
                primary.append({
                    'keyword': keyword,
                    'density': kw_density,
                    'tf_idf': kw_tf_idf,
                    'is_target': is_target,
                    'optimization_status': self._get_optimization_status(kw_density, 'primary')
                })
        
        # Sort by relevance (TF-IDF score)
        primary.sort(key=lambda x: x['tf_idf'], reverse=True)
        return primary[:5]  # Top 5 primary keywords
    
    def _classify_secondary_keywords(self, keywords: List[str], density: Dict[str, float],
                                   tf_idf: Dict[str, float], primary_keywords: List[Dict]) -> List[Dict[str, any]]:
        """Classify secondary keywords"""
        primary_kw_list = [kw['keyword'] for kw in primary_keywords]
        secondary = []
        
        for keyword in keywords:
            if keyword in primary_kw_list:
                continue
                
            kw_density = density.get(keyword, 0)
            kw_tf_idf = tf_idf.get(keyword, 0)
            
            # Secondary keyword criteria
            if (kw_density >= self.optimal_density['secondary'][0] or
                kw_tf_idf > 0.005):
                
                secondary.append({
                    'keyword': keyword,
                    'density': kw_density,
                    'tf_idf': kw_tf_idf,
                    'optimization_status': self._get_optimization_status(kw_density, 'secondary')
                })
        
        secondary.sort(key=lambda x: x['tf_idf'], reverse=True)
        return secondary[:10]  # Top 10 secondary keywords
    
    def _find_semantic_keywords(self, tokens: List[str], main_keywords: List[Dict],
                               language: str) -> List[Dict[str, any]]:
        """Find semantically related keywords"""
        # Simplified semantic analysis
        # In production, use word embeddings or semantic similarity models
        
        main_kw_tokens = set()
        for kw_dict in main_keywords:
            main_kw_tokens.update(kw_dict['keyword'].split())
        
        # Find related words based on co-occurrence
        semantic_candidates = []
        token_freq = Counter(tokens)
        
        for token, freq in token_freq.most_common(50):
            if (token not in main_kw_tokens and 
                freq >= 3 and 
                len(token) > 3):
                semantic_candidates.append({
                    'keyword': token,
                    'frequency': freq,
                    'relevance_score': freq / len(tokens)
                })
        
        return semantic_candidates[:15]  # Top 15 semantic keywords
    
    def _get_optimization_status(self, density: float, keyword_type: str) -> str:
        """Get optimization status for keyword density"""
        optimal_range = self.optimal_density[keyword_type]
        
        if density < optimal_range[0]:
            return 'under_optimized'
        elif density > optimal_range[1]:
            return 'over_optimized'
        else:
            return 'optimized'
    
    def _calculate_competitive_score(self, primary: List[Dict], secondary: List[Dict],
                                   density: Dict[str, float]) -> float:
        """Calculate competitive keyword score"""
        score = 0.0
        
        # Primary keywords score (60% weight)
        primary_score = 0
        for kw in primary:
            if kw['optimization_status'] == 'optimized':
                primary_score += 20
            elif kw['optimization_status'] == 'under_optimized':
                primary_score += 10
            # Over-optimized gets 0 points
        
        score += min(primary_score, 60)
        
        # Secondary keywords score (30% weight)
        secondary_score = 0
        for kw in secondary:
            if kw['optimization_status'] == 'optimized':
                secondary_score += 3
            elif kw['optimization_status'] == 'under_optimized':
                secondary_score += 1.5
        
        score += min(secondary_score, 30)
        
        # Keyword diversity score (10% weight)
        total_keywords = len(primary) + len(secondary)
        if total_keywords >= 10:
            score += 10
        elif total_keywords >= 5:
            score += 5
        
        return min(score, 100.0)
    
    def _generate_optimization_suggestions(self, primary: List[Dict], secondary: List[Dict],
                                         density: Dict[str, float], positions: Dict[str, List[int]]) -> List[str]:
        """Generate actionable keyword optimization suggestions"""
        suggestions = []
        
        # Primary keyword suggestions
        for kw in primary:
            keyword = kw['keyword']
            status = kw['optimization_status']
            
            if status == 'under_optimized':
                suggestions.append(f"Aumenta la densidad de '{keyword}' (actual: {kw['density']:.1f}%, óptimo: 1-3%)")
            elif status == 'over_optimized':
                suggestions.append(f"Reduce la densidad de '{keyword}' (actual: {kw['density']:.1f}%, óptimo: 1-3%)")
        
        # Position-based suggestions
        for keyword, pos_list in positions.items():
            if pos_list and pos_list[0] > 50:  # Keyword appears late in content
                suggestions.append(f"Mueve '{keyword}' hacia el inicio del contenido para mejor SEO")
        
        # General suggestions
        if len(primary) < 3:
            suggestions.append("Añade más palabras clave primarias relevantes")
        
        if len(secondary) < 5:
            suggestions.append("Incluye más palabras clave secundarias para ampliar el alcance")
        
        return suggestions

# Singleton instance
keyword_analysis_engine = RealKeywordAnalysisEngine()
